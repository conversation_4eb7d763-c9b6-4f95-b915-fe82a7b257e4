import bpy
import bmesh
import mathutils
from mathutils import Vector
import os

def clear_scene():
    """Clear all mesh objects from the scene"""
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False, confirm=False)
    print("Scene cleared")

def create_human_base_mesh():
    """Create a basic human-shaped mesh using metaballs and convert to mesh"""
    # Add metaballs for basic human shape
    bpy.ops.object.metaball_add(type='BALL', location=(0, 0, 1.6))  # Head
    head = bpy.context.active_object
    head.data.elements[0].radius = 0.12
    
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0, 0, 1.35))  # Neck
    neck = bpy.context.active_object
    neck.data.elements[0].radius = 0.06
    neck.data.elements[0].size_z = 0.3
    
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0, 0, 1.1))  # Upper torso
    torso_upper = bpy.context.active_object
    torso_upper.data.elements[0].radius = 0.18
    torso_upper.data.elements[0].size_z = 0.6
    
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0, 0, 0.7))  # Lower torso
    torso_lower = bpy.context.active_object
    torso_lower.data.elements[0].radius = 0.15
    torso_lower.data.elements[0].size_z = 0.4
    
    # Arms
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0.25, 0, 1.15))  # Right upper arm
    r_upper_arm = bpy.context.active_object
    r_upper_arm.data.elements[0].radius = 0.06
    r_upper_arm.data.elements[0].size_z = 0.5
    
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0.3, 0, 0.8))  # Right lower arm
    r_lower_arm = bpy.context.active_object
    r_lower_arm.data.elements[0].radius = 0.05
    r_lower_arm.data.elements[0].size_z = 0.4
    
    bpy.ops.object.metaball_add(type='BALL', location=(0.32, 0, 0.55))  # Right hand
    r_hand = bpy.context.active_object
    r_hand.data.elements[0].radius = 0.06
    
    # Mirror for left arm
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(-0.25, 0, 1.15))  # Left upper arm
    l_upper_arm = bpy.context.active_object
    l_upper_arm.data.elements[0].radius = 0.06
    l_upper_arm.data.elements[0].size_z = 0.5
    
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(-0.3, 0, 0.8))  # Left lower arm
    l_lower_arm = bpy.context.active_object
    l_lower_arm.data.elements[0].radius = 0.05
    l_lower_arm.data.elements[0].size_z = 0.4
    
    bpy.ops.object.metaball_add(type='BALL', location=(-0.32, 0, 0.55))  # Left hand
    l_hand = bpy.context.active_object
    l_hand.data.elements[0].radius = 0.06
    
    # Legs
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0.08, 0, 0.35))  # Right upper leg
    r_upper_leg = bpy.context.active_object
    r_upper_leg.data.elements[0].radius = 0.08
    r_upper_leg.data.elements[0].size_z = 0.7
    
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0.08, 0, -0.15))  # Right lower leg
    r_lower_leg = bpy.context.active_object
    r_lower_leg.data.elements[0].radius = 0.06
    r_lower_leg.data.elements[0].size_z = 0.6
    
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0.08, 0.08, -0.52))  # Right foot
    r_foot = bpy.context.active_object
    r_foot.data.elements[0].radius = 0.05
    r_foot.data.elements[0].size_y = 0.3
    
    # Mirror for left leg
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(-0.08, 0, 0.35))  # Left upper leg
    l_upper_leg = bpy.context.active_object
    l_upper_leg.data.elements[0].radius = 0.08
    l_upper_leg.data.elements[0].size_z = 0.7
    
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(-0.08, 0, -0.15))  # Left lower leg
    l_lower_leg = bpy.context.active_object
    l_lower_leg.data.elements[0].radius = 0.06
    l_lower_leg.data.elements[0].size_z = 0.6
    
    bpy.ops.object.metaball_add(type='ELLIPSOID', location=(-0.08, 0.08, -0.52))  # Left foot
    l_foot = bpy.context.active_object
    l_foot.data.elements[0].radius = 0.05
    l_foot.data.elements[0].size_y = 0.3
    
    # Select all metaballs and convert to mesh
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.convert(target='MESH')
    
    # Join all objects into one
    bpy.ops.object.join()
    
    # Get the resulting mesh
    human_mesh = bpy.context.active_object
    human_mesh.name = "HumanBase"
    
    print("Base human mesh created")
    return human_mesh

def refine_mesh(obj):
    """Apply subdivision and smoothing to the mesh"""
    bpy.context.view_layer.objects.active = obj
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Select all and recalculate normals
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.normals_consistent(inside=False)
    
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Add subdivision surface modifier
    subdiv_mod = obj.modifiers.new(name="Subdivision", type='SUBSURF')
    subdiv_mod.levels = 2
    subdiv_mod.render_levels = 3
    
    # Apply smooth shading
    bpy.ops.object.shade_smooth()
    
    print(f"Mesh {obj.name} refined with subdivision and smooth shading")

def separate_shirt_pants(human_mesh):
    """Separate the human mesh into shirt and pants at the waistline"""
    bpy.context.view_layer.objects.active = human_mesh
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Deselect all
    bpy.ops.mesh.select_all(action='DESELECT')
    
    # Select vertices below waist (z < 0.5) for pants
    bpy.ops.mesh.select_all(action='DESELECT')
    bm = bmesh.from_mesh(human_mesh.data)
    bm.faces.ensure_lookup_table()
    
    # Select faces for pants (lower body)
    for face in bm.faces:
        center_z = sum(v.co.z for v in face.verts) / len(face.verts)
        if center_z < 0.5:  # Below waist
            face.select = True
    
    bmesh.to_mesh(bm, human_mesh.data)
    bm.free()
    
    # Separate selected faces
    bpy.ops.mesh.separate(type='SELECTED')
    
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Get the separated objects
    objects = [obj for obj in bpy.context.selected_objects if obj.type == 'MESH']
    
    # Identify shirt and pants based on their center
    shirt = None
    pants = None
    
    for obj in objects:
        # Calculate center Z
        center_z = sum(v.co.z for v in obj.data.vertices) / len(obj.data.vertices)
        if center_z > 0.5:
            shirt = obj
            shirt.name = "Shirt"
        else:
            pants = obj
            pants.name = "Pants"
    
    print("Mesh separated into Shirt and Pants")
    return shirt, pants

def add_thickness(obj, thickness=0.003):
    """Add solidify modifier to make mesh suitable for 3D printing"""
    bpy.context.view_layer.objects.active = obj

    # Add solidify modifier
    solidify_mod = obj.modifiers.new(name="Solidify", type='SOLIDIFY')
    solidify_mod.thickness = thickness
    solidify_mod.offset = 0  # Centered
    solidify_mod.use_even_offset = True
    solidify_mod.use_quality_normals = True

    print(f"Added solidify modifier to {obj.name}")

def create_materials():
    """Create materials for shirt and pants"""
    # Create shirt material (light gray)
    shirt_mat = bpy.data.materials.new(name="ShirtMaterial")
    shirt_mat.use_nodes = True
    shirt_mat.node_tree.clear()

    # Add principled BSDF
    bsdf_shirt = shirt_mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
    output_shirt = shirt_mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')

    # Set shirt properties
    bsdf_shirt.inputs['Base Color'].default_value = (0.9, 0.9, 0.9, 1.0)  # Light gray
    bsdf_shirt.inputs['Roughness'].default_value = 0.8
    bsdf_shirt.inputs['Metallic'].default_value = 0.0

    # Connect nodes
    shirt_mat.node_tree.links.new(bsdf_shirt.outputs['BSDF'], output_shirt.inputs['Surface'])

    # Create pants material (dark blue)
    pants_mat = bpy.data.materials.new(name="PantsMaterial")
    pants_mat.use_nodes = True
    pants_mat.node_tree.clear()

    # Add principled BSDF
    bsdf_pants = pants_mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
    output_pants = pants_mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')

    # Set pants properties
    bsdf_pants.inputs['Base Color'].default_value = (0.1, 0.1, 0.4, 1.0)  # Dark blue
    bsdf_pants.inputs['Roughness'].default_value = 0.7
    bsdf_pants.inputs['Metallic'].default_value = 0.0

    # Connect nodes
    pants_mat.node_tree.links.new(bsdf_pants.outputs['BSDF'], output_pants.inputs['Surface'])

    print("Materials created: ShirtMaterial and PantsMaterial")
    return shirt_mat, pants_mat

def assign_materials(shirt, pants, shirt_mat, pants_mat):
    """Assign materials to the respective meshes"""
    # Assign shirt material
    if shirt:
        shirt.data.materials.clear()
        shirt.data.materials.append(shirt_mat)
        print(f"Material assigned to {shirt.name}")

    # Assign pants material
    if pants:
        pants.data.materials.clear()
        pants.data.materials.append(pants_mat)
        print(f"Material assigned to {pants.name}")

def clean_mesh(obj):
    """Clean up mesh geometry to ensure it's manifold"""
    bpy.context.view_layer.objects.active = obj
    bpy.ops.object.mode_set(mode='EDIT')

    # Select all
    bpy.ops.mesh.select_all(action='SELECT')

    # Remove doubles
    bpy.ops.mesh.remove_doubles(threshold=0.001)

    # Fill holes
    bpy.ops.mesh.fill_holes(sides=0)

    # Recalculate normals
    bpy.ops.mesh.normals_consistent(inside=False)

    bpy.ops.object.mode_set(mode='OBJECT')
    print(f"Mesh {obj.name} cleaned and made manifold")

def scale_to_human_size():
    """Scale the entire scene to human proportions (1.7-1.8m tall)"""
    # Select all mesh objects
    bpy.ops.object.select_all(action='DESELECT')
    for obj in bpy.context.scene.objects:
        if obj.type == 'MESH':
            obj.select_set(True)

    # Calculate current height
    if bpy.context.selected_objects:
        # Get bounding box of all selected objects
        min_z = min(obj.bound_box[0][2] + obj.location.z for obj in bpy.context.selected_objects for v in obj.bound_box)
        max_z = max(obj.bound_box[6][2] + obj.location.z for obj in bpy.context.selected_objects for v in obj.bound_box)
        current_height = max_z - min_z

        # Target height (1.75m)
        target_height = 1.75
        scale_factor = target_height / current_height

        # Apply scaling
        bpy.ops.transform.resize(value=(scale_factor, scale_factor, scale_factor))

        print(f"Scaled mannequin to {target_height}m tall (scale factor: {scale_factor:.3f})")

def export_glb(filename="human_mannequin.glb"):
    """Export the mannequin as GLB file"""
    # Select all mesh objects
    bpy.ops.object.select_all(action='DESELECT')
    for obj in bpy.context.scene.objects:
        if obj.type == 'MESH':
            obj.select_set(True)

    # Get the current blend file directory or use current working directory
    filepath = os.path.join(bpy.path.abspath("//"), filename)
    if not os.path.dirname(filepath):
        filepath = os.path.join(os.getcwd(), filename)

    # Export as GLB
    bpy.ops.export_scene.gltf(
        filepath=filepath,
        use_selection=True,
        export_materials='EXPORT',
        export_colors=True,
        export_normals=True,
        export_tangents=True,
        export_texcoords=True,
        export_apply=True
    )

    print(f"Exported mannequin to: {filepath}")
    return filepath

def main():
    """Main function to create the human mannequin"""
    try:
        print("=== Starting Human Mannequin Generation ===")

        # Step 1: Clear the scene
        clear_scene()

        # Step 2: Create base human mesh
        human_mesh = create_human_base_mesh()

        # Step 3: Refine the mesh
        refine_mesh(human_mesh)

        # Step 4: Separate into shirt and pants
        shirt, pants = separate_shirt_pants(human_mesh)

        if not shirt or not pants:
            raise Exception("Failed to separate mesh into shirt and pants")

        # Step 5: Clean up both meshes
        clean_mesh(shirt)
        clean_mesh(pants)

        # Step 6: Add thickness to both meshes
        add_thickness(shirt, thickness=0.003)  # 3mm thickness
        add_thickness(pants, thickness=0.003)  # 3mm thickness

        # Step 7: Refine both separated meshes
        refine_mesh(shirt)
        refine_mesh(pants)

        # Step 8: Create materials
        shirt_mat, pants_mat = create_materials()

        # Step 9: Assign materials
        assign_materials(shirt, pants, shirt_mat, pants_mat)

        # Step 10: Scale to human proportions
        scale_to_human_size()

        # Step 11: Export as GLB
        export_path = export_glb("human_mannequin.glb")

        print("=== Human Mannequin Generation Complete ===")
        print(f"Created objects: {shirt.name}, {pants.name}")
        print(f"Exported to: {export_path}")
        print("Mannequin is ready for 3D printing or rendering!")

        return True

    except Exception as e:
        print(f"Error during mannequin generation: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# Execute the script
if __name__ == "__main__":
    main()
